import {ref} from 'vue';
import {defineStore} from 'pinia';
import {useServicesStore} from "@/stores/services";
import { BaseApiServiceV4 } from "@/services/api/v4/BaseApiServiceV4";
import {useCompanyStore} from "@/stores/company";
import {useUserStore} from "@/stores/user";
import { useBillingStore } from "@/stores/billing.js";
import { useCompanyLicenseStore } from "@/stores/company-licenses.js";
import { useCompanyMediaStore } from "@/stores/company-media.js";
import { useCompanyUsersStore } from "@/stores/company-users.js";
import { useErrorStore } from "@/stores/errors.js";
import { useLocalityDataStore } from "@/stores/locality-data.js";
import { useLocationsStore } from "@/stores/locations.js";
import { useNotificationSettingsStore } from "@/stores/notification-settings.js";
import { useProfitabilityAssumptionsStore } from "@/stores/profitability-assumptions.js";
import { useProfitabilityFilterStore } from "@/stores/profitability-filters.js";
import { useReferenceDataStore } from "@/stores/reference-data.js";
import * as Sentry from "@/sentry";
import {useRouter} from "vue-router";

export const useAuthStore = defineStore('auth', () => {
    const services = useServicesStore();

    const token = ref(false);
    const expiry = ref(null);
    const isShadower = ref(false);

    const company = useCompanyStore();
    const user = useUserStore();
    const billing = useBillingStore();
    const companyLicenses = useCompanyLicenseStore();
    const companyMedia = useCompanyMediaStore();
    const companyUsers = useCompanyUsersStore();
    const errors = useErrorStore();
    const localityData = useLocalityDataStore();
    const locations = useLocationsStore();
    const notificationSettings = useNotificationSettingsStore();
    const profitabilityAssumptions = useProfitabilityAssumptionsStore();
    const profitabilityFilters = useProfitabilityFilterStore();
    const referenceData = useReferenceDataStore();

    const router = useRouter();

    function set(payload) {
        token.value = payload.token ?? token.value;
        expiry.value = payload.expiry ?? expiry.value;
        isShadower.value = payload.is_shadower ?? isShadower.value;

        services.apiService.setBearer(payload.token);
        services.apiServiceV4.setBearer(payload.token);
    }

    function isSessionValid() {
        const now = (new Date()).valueOf() / 1000;
        const expireAt = expiry.value ?? 0;

        return now < expireAt;
    }

    async function updatePassword(payload) {
        const resp = await services.apiService.updatePassword(payload).catch(e => e);

        if (resp.data?.data?.status) {
            return resp.data.data;
        }
        else {
            return BaseApiServiceV4.transformErrorResponse(resp);
        }
    }

    async function requestPasswordReset(email) {
        const resp = await services.apiService.requestPasswordReset(email).catch(e => e);
        if (resp.data?.data?.status) {
            return { status: true }
        }
        else {
            return BaseApiServiceV4.transformErrorResponse(resp);
        }
    }

    function logout() {
        this.$reset();
        company.$reset();
        user.$reset();
        billing.$reset();
        companyLicenses.$reset();
        companyMedia.$reset();
        companyUsers.$reset();
        errors.$reset();
        localityData.$reset();
        locations.$reset();
        notificationSettings.$reset();
        profitabilityAssumptions.$reset();
        profitabilityFilters.$reset();
        referenceData.$reset();

        Sentry.removeUserFromContext()

        router.push({name: 'login'});
    }

    function $reset() {
        token.value = false;
        expiry.value = null;
    }

    return {
        token,
        expiry,
        isShadower,

        set,
        isSessionValid,
        updatePassword,
        requestPasswordReset,
        $reset,
        logout,
    };
});