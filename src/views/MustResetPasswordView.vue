<template>
    <main class="h-screen w-full relative bg-image-gradient">
        <GlobalAlerts class="z-[1000]"/>
        <div class="relative flex h-full justify-center items-center p-3">
            <div class="pb-8 relative">
                <div class="absolute w-full h-full" v-if="saving">
                    <LoadingSpinner :small="true"/>
                </div>
                <div class="bg-white p-10 py-10 gap-6 rounded-xl relative md:w-128">
                    <!-- Header -->
                    <div class="mb-6">
                        <h1 class="text-2xl font-semibold text-gray-900 mb-2">Update Your Password</h1>
                        <p class="text-gray-600 mb-1">{{ userEmail }}</p>
                        <p class="text-gray-600 text-sm">Please reset your password to login.</p>
                    </div>

                    <div 
                        class="flex flex-col gap-y-4"
                        :class="[saving && 'opacity-50 grayscale-[50%] pointer-events-none']"
                        @keyup.enter="submitPasswordUpdate"
                    >
                        <!-- Update Password Field -->
                        <div class="flex flex-col gap-1">
                            <label class="text-sm font-medium text-gray-700">New Password</label>
                            <div class="relative">
                                <input
                                    v-model="password"
                                    type="password"
                                    class="w-full pl-4 pr-10 rounded border border-gray-300 bg-gray-50 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-fixr focus:border-transparent"
                                    @input="validatePassword"
                                />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Password Requirements -->
                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-700 mb-3">Password requirements</p>
                            <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <div class="w-5 h-5 rounded-full flex items-center justify-center"
                                         :class="passwordValidation.hasMinLength ? 'bg-blue-fixr' : 'bg-gray-300'">
                                        <svg v-if="passwordValidation.hasMinLength" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-gray-600">More than 8 characters</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="w-5 h-5 rounded-full flex items-center justify-center"
                                         :class="passwordValidation.hasNumber ? 'bg-blue-fixr' : 'bg-gray-300'">
                                        <svg v-if="passwordValidation.hasNumber" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-gray-600">Must include a number</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="w-5 h-5 rounded-full flex items-center justify-center"
                                         :class="passwordValidation.hasSpecialChar ? 'bg-blue-fixr' : 'bg-gray-300'">
                                        <svg v-if="passwordValidation.hasSpecialChar" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-gray-600">Must include a special character</span>
                                </div>
                            </div>
                        </div>

                        <!-- Confirm Password Field -->
                        <div class="flex flex-col gap-1">
                            <label class="text-sm font-medium text-gray-700">Confirm Password</label>
                            <div class="relative">
                                <input
                                    v-model="confirmPassword"
                                    type="password"
                                    class="w-full pl-4 pr-10 rounded border border-gray-300 bg-gray-50 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-fixr focus:border-transparent"
                                />
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Remember Me Checkbox -->
                        <div class="flex items-center gap-2 mt-2">
                            <input 
                                v-model="rememberMe"
                                type="checkbox" 
                                id="remember-me"
                                class="w-4 h-4 text-blue-fixr bg-gray-100 border-gray-300 rounded focus:ring-blue-fixr"
                            />
                            <label for="remember-me" class="text-sm text-gray-600">Remember me</label>
                        </div>

                        <!-- Update Password Button -->
                        <button
                            @click.prevent="submitPasswordUpdate"
                            :disabled="!isFormValid || saving"
                            class="w-full mt-6 py-3 px-4 rounded-md text-white font-medium transition duration-300"
                            :class="[
                                isFormValid && !saving
                                    ? 'bg-blue-fixr hover:bg-blue-fixr/95 cursor-pointer'
                                    : 'bg-gray-300 cursor-not-allowed'
                            ]"
                        >
                            <span v-if="saving">Updating Password...</span>
                            <span v-else>Update Password</span>
                        </button>
                    </div>

                    <!-- Footer -->
                    <div class="flex justify-center items-center gap-2 mt-8">
                        <span class="text-gray-400 text-sm">Powered by</span>
                        <fixr-roofing-calculator-logo/>
                    </div>
                </div>
            </div>
        </div>
    </main>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useAuthStore } from '@/stores/auth';
import { useAlertStore } from '@/stores/v4/alerts';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import GlobalAlerts from '@/components/v4/GlobalAlerts.vue';
import FixrRoofingCalculatorLogo from '@/components/shared/FixrRoofingCalculatorLogo.vue';

const router = useRouter();
const userStore = useUserStore();
const authStore = useAuthStore();
const alertStore = useAlertStore();

const password = ref('');
const confirmPassword = ref('');
const rememberMe = ref(false);
const saving = ref(false);

const userEmail = computed(() => userStore.email || '');

const passwordValidation = ref({
    hasMinLength: false,
    hasNumber: false,
    hasSpecialChar: false
});

const validatePassword = () => {
    const pwd = password.value;
    passwordValidation.value = {
        hasMinLength: pwd.length > 8,
        hasNumber: /\d/.test(pwd),
        hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd)
    };
};

const isFormValid = computed(() => {
    return passwordValidation.value.hasMinLength &&
           passwordValidation.value.hasNumber &&
           passwordValidation.value.hasSpecialChar &&
           password.value === confirmPassword.value &&
           password.value.length > 0;
});

const submitPasswordUpdate = async () => {
    if (!isFormValid.value || saving.value) return;
    
    saving.value = true;
    
    try {
        const result = await authStore.updatePassword({
            email: userStore.email,
            password: password.value,
            password_confirmation: confirmPassword.value
        });

        if (result.status) {
            alertStore.showInfo('Password updated successfully! Redirecting to dashboard...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            router.push({ name: 'dashboard' });
        } else {
            alertStore.showError(result.message || 'Failed to update password.');
        }
    } catch (error) {
        alertStore.showError('An error occurred while updating your password.');
    } finally {
        saving.value = false;
    }
};

onMounted(() => {
    // For demo purposes, we'll allow access even without a user email
    // In production, you might want to redirect to login if no user email
    // if (!userStore.email) {
    //     router.push({ name: 'login' });
    // }
});
</script>

<style scoped>
.bg-image-gradient {
    background: linear-gradient(360deg, #001B35 12.49%, rgba(0, 35, 68, 0.85) 63.1%, rgba(0, 79, 155, 0.8) 137.42%), url("/login_background.png");
    background-size: cover;
    background-position: center;
}
</style>
